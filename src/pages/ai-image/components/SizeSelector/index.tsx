import React from 'react';
import { CheckOutlined } from '@ali/ding-icons';
import './index.less';

interface SizeOption {
  id: string;
  name: string;
  ratio: string;
  description: string;
  width: number;
  height: number;
}

interface SizeSelectorProps {
  value: string;
  onChange: (size: string) => void;
  disabled?: boolean;
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  value,
  onChange,
  disabled = false,
}) => {
  // Size options with different aspect ratios
  const sizeOptions: SizeOption[] = [
    {
      id: '1:1',
      name: '正方形',
      ratio: '1:1',
      description: '1024×1024',
      width: 1024,
      height: 1024,
    },
    {
      id: '3:4',
      name: '竖版',
      ratio: '3:4',
      description: '768×1024',
      width: 768,
      height: 1024,
    },
    {
      id: '4:3',
      name: '横版',
      ratio: '4:3',
      description: '1024×768',
      width: 1024,
      height: 768,
    },
    {
      id: '16:9',
      name: '宽屏',
      ratio: '16:9',
      description: '1024×576',
      width: 1024,
      height: 576,
    },
  ];

  // Handle size selection
  const handleSizeSelect = (sizeId: string) => {
    if (!disabled) {
      onChange(sizeId);
    }
  };

  // Render size option
  const renderSizeOption = (option: SizeOption) => {
    const isSelected = value === option.id;

    return (
      <div
        key={option.id}
        className={`size-option ${isSelected ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
        onClick={() => handleSizeSelect(option.id)}
      >
        <div className="size-preview">
          <div
            className="size-rectangle"
            style={{
              aspectRatio: `${option.width} / ${option.height}`,
            }}
          >
            {isSelected && (
              <div className="selected-indicator">
                <CheckOutlined />
              </div>
            )}
          </div>
        </div>

        <div className="size-info">
          <div className="size-name">{option.name}</div>
          <div className="size-ratio">{option.ratio}</div>
          <div className="size-description">{option.description}</div>
        </div>
      </div>
    );
  };

  return (
    <div className={`size-selector ${disabled ? 'disabled' : ''}`}>
      {sizeOptions.map(renderSizeOption)}
    </div>
  );
};

export default SizeSelector;
