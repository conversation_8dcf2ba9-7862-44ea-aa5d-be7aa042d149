import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, To<PERSON>, Config<PERSON><PERSON>ider, Drawer } from 'dingtalk-design-mobile';
import { Popover } from 'dingtalk-design-desktop';
import { isMobileDevice } from '@/utils/jsapi';
import { InformationThereOutlined, CopyOutlined, CheckCircleFilled, CloseCircledFilled } from '@ali/ding-icons';
import ImageUploader from '@/pages/aiVideo/components/ImageUploader';
import BackgroundSelector from '../BackgroundSelector';
import SizeSelector from '../SizeSelector';
import './index.less';

interface HelpContentItem {
  type: 'text' | 'image' | 'section' | 'examples';
  text?: string;
  src?: string;
  alt?: string;
  title?: string;
  items?: string[];
  status?: 'correct' | 'incorrect';
  images?: Array<{
    src: string;
    alt?: string;
    label: string;
  }>;
}

// Help content interface
interface HelpContent {
  title: string;
  content: React.ReactNode;
}

interface HelpContents {
  [key: string]: HelpContent;
}

interface ImageHelpProps {
  title: string;
  helpKey?: string;
  onlyTitle?: boolean;
}

// ImageForm props interface
interface ImageFormProps {
  state: {
    uploadedImage: string | null;
    positivePrompt: string;
    selectedBackground: string | null;
    imageSize: string;
    isGenerating: boolean;
    error: string | null;
  };
  onUpdate: (updates: any) => void;
  onGenerate: () => void;
}

const ImageForm: React.FC<ImageFormProps> = ({ state, onUpdate, onGenerate }) => {
  const [helpDrawerVisible, setHelpDrawerVisible] = useState(false);
  const [currentHelpContent, setCurrentHelpContent] = useState<HelpContent | null>(null);
  const [currentHelpKey, setCurrentHelpKey] = useState<string>('');
  const isMobile = isMobileDevice();

  useEffect(() => {

  }, []);

  // Help content data
  const helpContents: HelpContents = {
    imageUpload: {
      title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ImageSpecification'),
      content: [
        {
          type: 'section',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_MaterialType'),
          items: [i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_TheElementsAreSimpleThe'), i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ItIsRecommendedToUpload')],
        },
        {
          type: 'section',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_Size'),
          items: [i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_SupportsJpgPngFormatWith'), i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_TheShortSideIsNot')],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_CorrectExample'),
          status: 'correct',
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i1/O1CN01TiAyLW24kXS8EGPRy_!!6000000007429-2-tps-534-510.png',
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_HandheldProductWithClearText'),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN0187V8vc1mfoHQTOAii_!!6000000004982-2-tps-534-510.png',
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_TheProductIsClearAnd'),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN01szA2Su1Kq3u76Xqku_!!6000000001214-2-tps-534-510.png',
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelHalfLengthDisplayClothing'),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i2/O1CN015406fZ1ZjONAnRfML_!!6000000003230-2-tps-534-510.png',
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelTrialProducts'),
            }],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ErrorExample'),
          status: 'incorrect',
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i3/O1CN01OmVXMg1CXj7dSQDNL_!!6000000000091-2-tps-534-510.png',
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_StitchingPicture'),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i1/O1CN01k8SmyE1L7vLhAbYea_!!6000000001253-2-tps-534-510.png',
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_UnclearSubject'),
            }],

        }],
    },
    positivePrompt: {
      title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_CreativeDescriptionReference'),
      content: [
        {
          type: 'section',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_DescribeThePictureAndAction'),
          items: [],
        },
        {
          type: 'examples',
          title: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ReferenceExample'),
          status: 'correct',
          images: [
            {
              src: 'https://img.alicdn.com/imgextra/i2/O1CN01CObnsc1fBOfXHR5S7_!!6000000003968-1-tps-480-316.gif',
              alt: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_MenSlowlyPickUpThe'),
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptMenSlowlyPickUp'),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN01Jk5kuk1voMDFxu3Wy_!!6000000006219-1-tps-320-320.gif',
              alt: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_TheLensSlowlyRotatesTo'),
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptTheLensSlowlyRotates'),
            },
            {
              src: 'https://img.alicdn.com/imgextra/i4/O1CN01f8ASEy1eQgGjAGtxN_!!6000000003866-1-tps-320-320.gif',
              alt: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelPosingShowingYogaClothes'),
              label: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptModelPosingShowingYoga'),
            }],

        }],

    },
  };

  // Handle image upload
  const handleImageUpload = (imageUrl: string | null) => {
    onUpdate({ uploadedImage: imageUrl });
  };

  // Handle prompt change
  const handlePromptChange = (field: string, value: string) => {
    onUpdate({ [field]: value });
  };

  const handleCopyPrompt = (promptText: string) => {
    // Update positive prompt field
    onUpdate({ positivePrompt: promptText });

    // Show success message
    Toast.success({
      content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_CopiedToCreativeDescription'),
      duration: 2,
      position: 'top',
      maskClickable: true,
    });
  };

  // Handle help icon click
  const handleHelpClick = (helpKey: string) => {
    const helpContent = helpContents[helpKey];
    if (helpContent) {
      setCurrentHelpContent(helpContent);
      setCurrentHelpKey(helpKey);
      if (isMobile) {
        setHelpDrawerVisible(true);
      }
    }
  };

  // Handle background selection
  const handleBackgroundChange = (background: string | null) => {
    onUpdate({ selectedBackground: background });
  };

  // Handle size selection
  const handleSizeChange = (size: string) => {
    onUpdate({ imageSize: size });
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!state.uploadedImage) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseUploadAnImage'),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    if (!state.positivePrompt?.trim()) {
      Toast.info({
        content: '请输入创意描述',
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    if (state.isGenerating) {
      return;
    }

    onGenerate();
  };

  // Form title component with help icon
  const FormTitle: React.FC<ImageHelpProps> = ({ title, helpKey, onlyTitle }) => {
    const helpContent = helpContents[helpKey];

    const InfoIcon = () => (
      <InformationThereOutlined
        className="form-title-info-icon"
        onClick={() => handleHelpClick(helpKey)}
      />
    );

    if (isMobile) {
      return (
        <div className="form-title">
          <span className="form-title-text">{title}</span>
          {!onlyTitle && <InfoIcon />}
        </div>
      );
    } else {
      // PC端：使用 Popover 组件，鼠标悬停显示在右边
      const renderHelpContent = () => (
        <div className="help-popover-content">
          {helpContent?.content.map((item: HelpContentItem, index: number) => {
            const renderContent = () => {
              if (item.type === 'text') {
                return <p className="help-text">{item.text}</p>;
              }

              if (item.type === 'image') {
                return <img src={item.src} alt={item.alt} className="help-content-image" />;
              }

              if (item.type === 'section') {
                return (
                  <div className="help-section">
                    {item.items && item.items.length > 0 ? (
                      <>
                        <h4 className="help-section-title">{item.title}</h4>
                        <ul className="help-section-list">
                          {item.items.map((listItem, listIndex) => (
                            <li key={`section-item-${listIndex}`} className="help-section-item">
                              {listItem}
                            </li>
                          ))}
                        </ul>
                      </>
                    ) : (
                      <p className="help-section-description">{item.title}</p>
                    )}
                  </div>
                );
              }

              if (item.type === 'examples') {
                return (
                  <div
                    className={`help-examples ${item.status === 'correct' ? 'correct' : 'incorrect'}`}
                  >
                    <div className="help-examples-header">
                      <span
                        className={`help-examples-icon ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        {item.status === 'correct' ? <CheckCircleFilled /> : <CloseCircledFilled />}
                      </span>
                      <h4
                        className={`help-examples-title ${
                          item.status === 'correct' ? 'correct' : 'incorrect'
                        }`}
                      >
                        {item.title}
                      </h4>
                    </div>
                    <div
                      className={`help-examples-grid ${
                        helpKey === 'positivePrompt' ? 'single-column' : ''
                      }`}
                    >
                      {item.images?.map((image, imageIndex) => (
                        <div key={`example-image-${imageIndex}`} className="help-example-item">
                          <div className="help-example-label-container">
                            <p className="help-example-label">{image.label}</p>
                            {image.alt && (
                              <button
                                className="help-example-copy-btn"
                                onClick={() => handleCopyPrompt(image.alt!)}
                                title={i18next.t(
                                  'j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription',
                                )}
                              >
                                <CopyOutlined className="copy-icon" />
                              </button>
                            )}
                          </div>
                          <div className="help-example-image-placeholder">
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="help-example-image"
                              loading="lazy"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              }

              return null;
            };

            return (
              <div key={`help-content-${index}`} className="help-content-item">
                {renderContent()}
              </div>
            );
          })}
        </div>
      );

      return (
        <div className="form-title">
          <span className="form-title-text">{title}</span>
          {!onlyTitle && (
            <Popover
              title={helpContent?.title}
              content={renderHelpContent()}
              placement="right"
              trigger="hover"
              arrow={false}
              overlayClassName="form-help-popover"
            >
              <InformationThereOutlined className="form-title-info-icon" />
            </Popover>
          )}
        </div>
      );
    }
  };

  return (
    <div className="image-form">
      <div className="form-container">
        {/* Image Upload Section */}
        <div className="form-section">
          <FormTitle
            title="上传原始图片"
            helpKey="imageUpload"
          />
          <ImageUploader value={state.uploadedImage} onChange={handleImageUpload} />
        </div>

        {/* Positive Prompt Section */}
        <div className="form-section">
          <FormTitle title="你的创意描述" helpKey="positivePrompt" />
          <div className="textarea-container">
            <textarea
              className="custom-textarea"
              placeholder="选择下方模板，或自由输入"
              onChange={(e) => handlePromptChange('positivePrompt', e.target.value)}
              maxLength={200}
            />

            <div className="char-count">{state.positivePrompt.length} / 200</div>
          </div>
        </div>

        {/* Background Selection Section */}
        <div className="form-section">
          <BackgroundSelector value={state.selectedBackground} onChange={handleBackgroundChange} />
        </div>

        {/* Size Selection Section */}
        <div className="form-section">
          <div className="duration-section">
            <div className="duration-label">图片尺寸</div>
            <SizeSelector value={state.imageSize} onChange={handleSizeChange} />
          </div>
        </div>

        {/* Error Display */}
        {state.error && <div className="error-message">{state.error}</div>}

        {/* Generate Button */}
        <div className="form-actions">
          <Button
            type="primary"
            size="large"
            className="generate-btn"
            loading={state.isGenerating}
            onClick={handleSubmit}
            disabled={!state.uploadedImage || !state.positivePrompt.trim() || state.isGenerating}
          >
            {state.isGenerating ? '生成中...' : '立即生成'}
          </Button>
        </div>
      </div>

      {/* Help Drawer for Mobile */}
      {isMobile && (
        <ConfigProvider config={{ adapt: false }}>
          <Drawer
            size="large"
            title={currentHelpContent?.title || '帮助信息'}
            visible={helpDrawerVisible}
            onVisibleChange={setHelpDrawerVisible}
          >
            {currentHelpContent?.content}
          </Drawer>
        </ConfigProvider>
      )}
    </div>
  );
};

export default ImageForm;
